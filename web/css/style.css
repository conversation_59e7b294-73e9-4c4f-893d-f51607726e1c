body {
  font-family: system-ui, -apple-system, <PERSON>go<PERSON>I, Roboto, sans-serif;
  margin: 2rem;
  background: var(--bg-secondary);
  color: var(--text-primary);
}

code {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1rem;
}

.card {
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 1.25rem;
  background: var(--bg-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.app-shell {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.app-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.app-header-top {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.app-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.nav-tabs {
  display: flex;
  gap: .5rem;
  flex-wrap: wrap;
}

.nav-tab {
  padding: 0.45rem 0.9rem;
  border: 1px solid var(--border-secondary);
  border-radius: 999px;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: .9rem;
  transition: background .2s, color .2s, border .2s;
}

.nav-tab:hover {
  background: var(--bg-accent);
  color: var(--text-primary);
}

.nav-tab.active {
  background: var(--accent-primary);
  color: var(--text-inverse);
  border-color: var(--accent-primary);
}

.view-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.view-section.hidden {
  display: none !important;
}

.panel {
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 1.2rem;
  background: var(--bg-primary);
  box-shadow: var(--shadow-sm);
}

.panel h2 {
  margin-top: 0;
  margin-bottom: .6rem;
  color: var(--text-primary);
}

.section-title-tight {
  margin: 0 0 .5rem 0;
}

.section-subheading {
  margin: 1rem 0 .5rem;
}

.logs-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.logs-controls {
  display: flex;
  gap: .6rem;
  flex-wrap: wrap;
  align-items: center;
}

.logs-list {
  display: flex;
  flex-direction: column;
  gap: .75rem;
}

.log-entry {
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: .9rem 1rem;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
  gap: .4rem;
}

.log-meta {
  display: flex;
  gap: .75rem;
  flex-wrap: wrap;
  font-size: .82rem;
  color: var(--text-secondary);
  align-items: center;
}

.log-level {
  padding: .1rem .5rem;
  border-radius: 999px;
  font-size: .75rem;
  text-transform: uppercase;
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.log-level.warn {
  background: color-mix(in srgb, var(--accent-warning) 15%, transparent);
  color: var(--accent-warning);
}

.log-level.error {
  background: color-mix(in srgb, var(--accent-danger) 15%, transparent);
  color: var(--accent-danger);
}

.log-level.info {
  background: color-mix(in srgb, var(--accent-primary) 15%, transparent);
  color: var(--accent-primary);
}

.log-source-pill {
  padding: .1rem .45rem;
  border-radius: 999px;
  font-size: .75rem;
  background: color-mix(in srgb, var(--accent-secondary) 12%, var(--bg-accent));
  color: var(--accent-secondary);
}

.log-source-pill.system {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-accent);
}

.log-device {
  font-weight: 600;
  color: var(--text-primary);
}

.log-message {
  font-size: .95rem;
  color: var(--text-primary);
  word-break: break-word;
}

.log-context {
  display: flex;
  flex-wrap: wrap;
  gap: .5rem;
  font-size: .8rem;
  color: var(--text-secondary);
}

.log-context span {
  background: var(--bg-tertiary);
  padding: .2rem .45rem;
  border-radius: 6px;
}

.device-toolbar {
  display: flex;
  gap: .75rem;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}

.device-toolbar-buttons {
  display: flex;
  gap: .5rem;
  flex-wrap: wrap;
}

.device-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  overflow: hidden;
}

.table-checkbox-column {
  width: 42px;
}

.device-table thead {
  background: var(--bg-secondary);
}

.device-table th, .device-table td {
  padding: .6rem .75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
  font-size: .9rem;
}

.device-table th {
  font-size: .78rem;
  text-transform: uppercase;
  letter-spacing: .04em;
  color: var(--text-secondary);
}

.device-table tbody tr:hover {
  background: var(--bg-tertiary);
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 640px;
}

.settings-group {
  display: flex;
  flex-direction: column;
  gap: .8rem;
}

.settings-group h3 {
  margin: 0;
  font-size: 1.05rem;
}

.settings-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1rem;
}

.settings-field {
  display: flex;
  flex-direction: column;
  gap: .4rem;
}

.settings-field label {
  font-weight: 600;
  color: var(--text-primary);
}

.settings-field input, .settings-field select {
  padding: .5rem .6rem;
  border: 1px solid var(--border-secondary);
  border-radius: 6px;
  font-size: .95rem;
}

.settings-field input:focus, .settings-field select:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px color-mix(in srgb, var(--accent-primary) 15%, transparent);
}

.settings-note {
  font-size: .8rem;
  color: var(--text-secondary);
}

.settings-actions {
  display: flex;
  gap: .75rem;
  align-items: center;
}

.insights-header {
  display: flex;
  flex-wrap: wrap;
  gap: .75rem;
  align-items: center;
}

.insights-summary {
  display: flex;
  flex-direction: column;
  gap: .3rem;
}

.insights-meta {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  font-size: .88rem;
  color: var(--text-secondary);
}

.insights-chart {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 1rem;
}

.empty-state {
  padding: 1.2rem;
  border: 1px dashed var(--border-accent);
  border-radius: 8px;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  font-size: .9rem;
}

.form-inline {
  display: flex;
  gap: .5rem;
  align-items: center;
  flex-wrap: wrap;
}

.muted-strong {
  color: var(--text-secondary);
  font-weight: 500;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.row {
  display: flex;
  gap: .5rem;
  align-items: center;
}

button {
  padding: .4rem .7rem;
  border: 1px solid var(--border-secondary);
  border-radius: 6px;
  background: var(--bg-tertiary);
  color: var(--text-primary);
  cursor: pointer;
}

button:hover {
  background: var(--bg-accent);
}

button.danger {
  background: color-mix(in srgb, var(--accent-danger) 12%, transparent);
  border-color: color-mix(in srgb, var(--accent-danger) 50%, transparent);
  color: var(--accent-danger);
}

button.danger:hover {
  background: color-mix(in srgb, var(--accent-danger) 22%, transparent);
}

button:disabled {
  opacity: .6;
  cursor: not-allowed;
}

.muted {
  color: var(--text-secondary);
  font-size: .9rem;
}

.muted-sm {
  font-size: .85rem;
}

.muted-xs {
  font-size: .8rem;
}

.latest-metrics {
  display: grid;
  gap: .5rem;
  margin: .75rem 0;
}

.metrics-compact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: .75rem;
}

.metric-compact-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: .5rem;
  background: var(--bg-secondary);
  border-radius: 6px;
  border: 1px solid var(--border-primary);
}

.metric-compact-label {
  font-size: .5rem;
  text-transform: uppercase;
  letter-spacing: .04em;
  color: var(--text-secondary);
  margin-bottom: .25rem;
}

.metric-compact-value {
  font-size: .9rem;
  font-weight: 600;
  color: var(--text-primary);
}

.metric-compact-unit {
  font-size: .75rem;
  color: var(--text-secondary);
  margin-left: .2rem;
}

/* Hidden metrics functionality */
.metric-compact-item.metric-hidden {
  display: none;
}

.metrics-toggle-container {
  margin-top: .5rem;
  text-align: center;
}

.metrics-toggle-btn {
  background: transparent;
  border: none;
  color: var(--accent-primary);
  font-size: .8rem;
  cursor: pointer;
  padding: .25rem .5rem;
  border-radius: 4px;
  transition: background .2s;
}

.metrics-toggle-btn:hover {
  background: var(--bg-tertiary);
  text-decoration: underline;
}

.metrics-toggle-tooltip {
  position: relative;
  display: inline-block;
}

.metrics-toggle-tooltip .tooltip-text {
  visibility: hidden;
  width: 220px;
  background-color: var(--text-primary);
  color: var(--text-inverse);
  text-align: center;
  border-radius: 6px;
  padding: .5rem;
  position: absolute;
  z-index: 1000;
  bottom: 125%;
  left: 50%;
  margin-left: -110px;
  opacity: 0;
  transition: opacity .3s;
  font-size: .75rem;
  line-height: 1.3;
}

.metrics-toggle-tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--text-primary) transparent transparent transparent;
}

.metrics-toggle-tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

.metrics-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: .4rem;
}

.metrics-header .metric-cell {
  font-size: .78rem;
  text-transform: uppercase;
  letter-spacing: .04em;
  color: var(--text-secondary);
}

.metrics-values .metric-cell {
  font-size: .95rem;
  color: var(--text-primary);
  font-weight: 600;
}

.metric-cell {
  padding: .1rem 0;
}

.action-flex{
  display: flex;
  gap: .5rem;
  flex-wrap: wrap;
}

.device-logs {
  margin-top: .6rem;
  border-top: 1px solid var(--border-primary);
  padding-top: .6rem;
  display: flex;
  flex-direction: column;
  gap: .35rem;
}

.device-log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: .82rem;
  color: var(--text-secondary);
}

.device-log-toggle {
  background: transparent;
  border: none;
  color: var(--accent-primary);
  font-size: .82rem;
  cursor: pointer;
  padding: 0;
}

.device-log-toggle:hover {
  text-decoration: underline;
}

.device-log-list {
  display: flex;
  flex-direction: column;
  gap: .35rem;
}

.device-log-entry {
  display: grid;
  grid-template-columns: 96px auto;
  gap: .5rem;
  font-size: .85rem;
  color: var(--text-primary);
}

.device-log-time {
  color: var(--text-secondary);
  font-family: ui-monospace, SFMono-Regular, Menlo, monospace;
  font-size: .8rem;
}

.device-log-level {
  text-transform: uppercase;
  font-weight: 600;
  margin-right: .4rem;
  font-size: .75rem;
  color: var(--text-secondary);
}

.device-log-message {
  word-break: break-word;
}

.device-log-body {
  display: flex;
  gap: .4rem;
}

.mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, monospace;
  font-size: .9rem;
}

.mono-input {
  font-family: ui-monospace, SFMono-Regular, Menlo, monospace;
  font-size: .9rem;
}

.hidden {
  display: none !important;
}

.btn {
  padding: 0.6rem 1rem;
  border-radius: 6px;
  border: 1px solid transparent;
  font-size: .95rem;
  cursor: pointer;
  transition: background .2s, color .2s, border .2s;
}

.btn-primary {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: var(--text-inverse);
}

.btn-primary:hover {
  background: var(--accent-primary-hover);
  border-color: var(--accent-primary-hover);
}

.btn-secondary {
  background: var(--accent-secondary);
  border-color: var(--accent-secondary);
  color: var(--text-inverse);
}

.btn-secondary:hover {
  filter: brightness(0.92);
}

.btn-outline {
  background: transparent;
  border-color: var(--accent-primary);
  color: var(--accent-primary);
}

.btn-outline:hover {
  background: var(--accent-primary);
  color: var(--text-inverse);
}

.btn-link {
  background: transparent;
  border-color: transparent;
  color: var(--accent-primary);
  padding-left: 0;
  padding-right: 0;
}

.btn-link:hover {
  background: transparent;
  text-decoration: underline;
}

.btn:disabled {
  opacity: .6;
  cursor: not-allowed;
}

.modal-backdrop {
  position: fixed;
  inset: 0;
  background: color-mix(in srgb, var(--text-primary) 35%, transparent);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-backdrop.hidden {
  display: none;
}

.modal {
  background: var(--bg-primary);
  border-radius: 8px;
  padding: 1.5rem;
  max-width: 420px;
  width: min(90vw, 420px);
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modal-wide {
  max-width: 520px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: .5rem;
}

.device-backup-section {
  border: 1px dashed var(--border-primary);
  border-radius: 8px;
  padding: .75rem;
  background: var(--bg-secondary);
  display: flex;
  flex-direction: column;
  gap: .5rem;
  margin-bottom: .6rem;
}

.device-backup-section .backup-status {
  font-size: .9rem;
  color: var(--text-secondary);
}

.backup-action-row {
  display: flex;
  flex-wrap: wrap;
  gap: .5rem;
  align-items: center;
}

.backup-history-list {
  display: flex;
  flex-direction: column;
  gap: .4rem;
  max-height: 340px;
  overflow-y: auto;
}

.backup-history-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: .75rem;
  padding: .6rem 0;
  border-bottom: 1px solid var(--border-primary);
}

.backup-history-row:last-child {
  border-bottom: none;
}

.backup-history-meta {
  display: flex;
  flex-direction: column;
  gap: .25rem;
}

.backup-history-actions {
  display: flex;
  gap: .5rem;
  align-items: center;
}

.backup-empty {
  color: var(--text-secondary);
  font-size: .9rem;
  padding: .6rem 0;
}

.badge-latest {
  background: color-mix(in srgb, var(--accent-success) 15%, transparent);
  color: var(--accent-success);
  padding: .1rem .4rem;
  border-radius: 999px;
  font-size: .7rem;
  text-transform: uppercase;
}

.toast-container {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: .5rem;
  z-index: 1100;
}

.toast {
  background: var(--text-primary);
  color: var(--text-inverse);
  padding: .75rem 1rem;
  border-radius: 6px;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: .75rem;
}

.toast button {
  padding: .4rem .7rem;
  border: 1px solid var(--border-secondary);
  border-radius: 6px;
  background: var(--bg-tertiary);
  color: var(--text-primary);
  cursor: pointer;
}

.toast button:hover {
  background: var(--bg-accent);
}

.toast.success {
  background: var(--accent-success);
}

.toast.error {
  background: var(--accent-danger);
}

.toast.info {
  background: var(--accent-primary);
}

.toast.warning {
  background: var(--accent-warning);
  color: var(--text-primary);
}

.link-unstyled {
  text-decoration: none;
  color: inherit;
}

.menu-wrapper {
  position: relative;
  margin-left: auto;
}

.menu-trigger {
  background: transparent;
  border: none;
  font-size: 1.2rem;
  line-height: 1;
  padding: .2rem .4rem;
  cursor: pointer;
  color: var(--text-secondary);
}

.menu-trigger:hover {
  background: var(--bg-tertiary);
  border-radius: 6px;
}

.menu-list {
  position: absolute;
  top: calc(100% + .3rem);
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-secondary);
  border-radius: 8px;
  min-width: 160px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 950;
}

.menu-list.hidden {
  display: none;
}

.menu-item {
  padding: .6rem .75rem;
  border: none;
  background: transparent;
  text-align: left;
  cursor: pointer;
  font-size: .95rem;
  color: var(--text-primary);
}

.menu-item:hover {
  background: var(--bg-tertiary);
}

.menu-item.danger {
  color: var(--accent-danger);
}

.menu-item.danger:hover {
  background: color-mix(in srgb, var(--accent-danger) 12%, transparent);
}

.drawer-backdrop {
  position: fixed;
  inset: 0;
  background: color-mix(in srgb, var(--text-primary) 45%, transparent);
  display: none;
  align-items: stretch;
  justify-content: flex-end;
  z-index: 1200;
}

.drawer-backdrop.active {
  display: flex;
}

.drawer-panel {
  width: min(640px, 100%);
  max-width: 640px;
  background: var(--bg-primary);
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-lg);
}

.drawer-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.drawer-title {
  margin: 0;
  font-size: 1.4rem;
}

.drawer-heading {
  margin: 0;
  font-size: 1.2rem;
}

.drawer-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.drawer-section {
  border-top: 1px solid var(--border-primary);
  padding-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.drawer-section-title {
  margin: 0;
}

.drawer-section-actions {
  display: flex;
  gap: .75rem;
  align-items: center;
}

.drawer-footer {
  padding: 1.25rem 1.5rem;
  border-top: 1px solid var(--border-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.drawer-action-buttons {
  display: flex;
  gap: .5rem;
}

.drawer-close {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  line-height: 1;
  color: var(--text-secondary);
}

.drawer-close:hover {
  color: var(--text-primary);
}

.stepper {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.stepper-item {
  flex: 1;
  padding: 0.6rem 0.75rem;
  border-radius: 999px;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  font-size: .9rem;
  text-align: center;
}

.stepper-item.active {
  background: var(--accent-primary);
  color: var(--text-inverse);
}

.stepper-item.completed {
  background: var(--accent-success);
  color: var(--text-inverse);
}

.edit-step {
  display: none;
}

.edit-step.active {
  display: block;
}

.form-group {
  margin-bottom: 1.1rem;
  display: flex;
  flex-direction: column;
  gap: .4rem;
}

.form-group-tight {
  margin-bottom: 0;
}

.form-group label {
  font-weight: 600;
}

.form-group input, .form-group select, .form-group textarea {
  padding: 0.6rem 0.7rem;
  border: 1px solid var(--border-secondary);
  border-radius: 6px;
  font-size: .95rem;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.form-group .help {
  font-size: .85rem;
  color: var(--text-secondary);
}

.form-group .error {
  font-size: .85rem;
  color: var(--accent-danger);
}

.ssh-key-field-controls {
  display: flex;
  gap: .5rem;
  align-items: center;
}

.ssh-key-field-controls select {
  flex: 1;
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: .3rem;
  padding: .3rem .55rem;
  border-radius: 999px;
  font-size: .72rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: .05em;
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.badge-label {
  display: inline-block;
}

.badge-icon {
  font-size: .9rem;
  line-height: 1;
}

.badge-router {
  background: color-mix(in srgb, var(--accent-primary) 12%, var(--bg-accent));
  color: var(--accent-primary);
}

.badge-switch {
  background: color-mix(in srgb, var(--accent-secondary) 12%, var(--bg-accent));
  color: var(--accent-secondary);
}

.badge-ap {
  background: color-mix(in srgb, var(--accent-success) 12%, var(--bg-accent));
  color: var(--accent-success);
}

.badge-firewall {
  background: color-mix(in srgb, var(--accent-danger) 12%, var(--bg-accent));
  color: var(--accent-danger);
}

.badge-server {
  background: color-mix(in srgb, var(--accent-success) 10%, var(--bg-accent));
  color: var(--accent-success);
}

.badge-gateway {
  background: color-mix(in srgb, var(--accent-warning) 12%, var(--bg-accent));
  color: var(--accent-warning);
}

.badge-modem {
  background: color-mix(in srgb, var(--accent-primary) 10%, var(--bg-accent));
  color: var(--accent-primary);
}

.badge-default {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.edit-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: .75rem;
  padding: 2rem 0;
  color: var(--text-secondary);
}

.spinner {
  border: 3px solid var(--bg-tertiary);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  width: 28px;
  height: 28px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.validation-results {
  display: flex;
  flex-direction: column;
  gap: .5rem;
}

.validation-item {
  padding: .6rem .75rem;
  border-radius: 6px;
  font-size: .95rem;
}

.validation-success {
  background: color-mix(in srgb, var(--accent-success) 12%, transparent);
  color: var(--accent-success);
  border: 1px solid color-mix(in srgb, var(--accent-success) 50%, transparent);
}

.validation-warning {
  background: color-mix(in srgb, var(--accent-warning) 15%, transparent);
  color: var(--accent-warning);
  border: 1px solid color-mix(in srgb, var(--accent-warning) 55%, transparent);
}

.validation-error {
  background: color-mix(in srgb, var(--accent-danger) 12%, transparent);
  color: var(--accent-danger);
  border: 1px solid color-mix(in srgb, var(--accent-danger) 50%, transparent);
}

.edit-summary {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: .4rem;
}

.key-modal-backdrop {
  position: fixed;
  inset: 0;
  background: color-mix(in srgb, var(--text-primary) 50%, transparent);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1300;
  padding: 1.5rem;
}

.key-modal-backdrop.active {
  display: flex;
}

.key-modal {
  width: min(560px, 95vw);
  background: var(--bg-primary);
  border-radius: 10px;
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

.key-modal header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.key-modal .modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.key-list {
  display: flex;
  flex-direction: column;
  gap: .75rem;
}

.key-card {
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: .9rem;
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  align-items: center;
}

.key-card-actions {
  display: flex;
  gap: .5rem;
}

.key-viewer {
  font-family: ui-monospace, SFMono-Regular, Menlo, monospace;
  background: var(--bg-secondary);
  border-radius: 6px;
  padding: .75rem;
  white-space: pre-wrap;
  word-break: break-all;
}

.task-section {
  margin-top: 1rem;
  padding-top: .8rem;
  border-top: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  gap: .6rem;
}

.task-section h4 {
  margin: 0;
  font-size: 1rem;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: .5rem;
}

.task-item {
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  background: var(--bg-tertiary);
  padding: .6rem .75rem;
  display: flex;
  flex-direction: column;
  gap: .4rem;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  gap: .5rem;
  align-items: flex-start;
}

.status-pill {
  padding: .2rem .55rem;
  border-radius: 999px;
  font-size: .75rem;
  text-transform: uppercase;
  letter-spacing: .05em;
}

.status-queued {
  background: color-mix(in srgb, var(--accent-warning) 15%, transparent);
  color: var(--accent-warning);
}

.status-running {
  background: color-mix(in srgb, var(--accent-primary) 18%, transparent);
  color: var(--accent-primary);
}

.status-done {
  background: color-mix(in srgb, var(--accent-success) 12%, transparent);
  color: var(--accent-success);
}

.status-error {
  background: color-mix(in srgb, var(--accent-danger) 12%, transparent);
  color: var(--accent-danger);
}

.task-output {
  font-family: ui-monospace, SFMono-Regular, Menlo, monospace;
  font-size: .8rem;
  color: var(--text-secondary);
  white-space: pre-wrap;
  max-height: 6rem;
  overflow: hidden;
}

.btn-danger {
  background: var(--accent-danger);
  border-color: var(--accent-danger);
  color: var(--text-inverse);
}

.btn-danger:hover {
  filter: brightness(0.9);
}

.task-toggle {
  align-self: flex-start;
  padding: .3rem .65rem;
  font-size: .8rem;
  border-radius: 6px;
  border: 1px solid var(--accent-primary);
  background: transparent;
  color: var(--accent-primary);
  cursor: pointer;
}

.task-toggle:hover {
  background: var(--accent-primary);
  color: var(--text-inverse);
}

.device-header {
  align-items: flex-start;
  gap: .75rem;
  margin-bottom: .75rem;
}

.device-header-info {
  display: flex;
  flex-direction: column;
  gap: .3rem;
  min-width: 0;
}

.device-title-row {
  display: flex;
  align-items: center;
  gap: .75rem;
  flex-wrap: wrap;
}

.device-name {
  font-size: 1.1rem;
  font-weight: 600;
}

.device-subtitle {
  font-size: .85rem;
  color: var(--text-secondary);
}

.modal-actions {
  display: flex;
  gap: .5rem;
  align-items: center;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.modal-actions-extra {
  display: flex;
  gap: .5rem;
  margin-right: auto;
  flex-wrap: wrap;
}

.modal-actions-main {
  display: flex;
  gap: .5rem;
  align-items: center;
}

.confirm-extra-block {
  display: flex;
  flex-direction: column;
  gap: .4rem;
}

.confirm-extra-block label {
  font-size: .8rem;
  color: var(--text-secondary);
}

.confirm-extra-block input {
  padding: .45rem .6rem;
  border: 1px solid var(--border-secondary);
  border-radius: 6px;
  font-size: .9rem;
  min-width: 220px;
}

.confirm-extra-block input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.metric-chart {
  margin-top: .6rem;
}

.metric-chart canvas {
  width: 100%;
  height: 50px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 6px;
}

.hardware-section {
  margin-top: .8rem;
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  background: var(--bg-secondary);
  padding: .8rem;
  display: flex;
  flex-direction: column;
  gap: .6rem;
}

.hardware-header h4 {
  margin: 0;
  font-size: .9rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: .04em;
}

.hardware-summary {
  display: flex;
  flex-wrap: wrap;
  gap: .4rem;
}

.hardware-pill {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: .25rem .55rem;
  border-radius: 999px;
  font-size: .75rem;
}

.interface-table-wrapper {
  overflow-x: auto;
}

.interface-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 480px;
}

.interface-table th, .interface-table td {
  padding: .4rem .5rem;
  border-bottom: 1px solid var(--border-primary);
  text-align: left;
  font-size: .8rem;
  white-space: nowrap;
}

.interface-table th {
  text-transform: uppercase;
  letter-spacing: .05em;
  font-size: .72rem;
  color: var(--text-secondary);
}

.interface-table tbody tr:hover {
  background: color-mix(in srgb, var(--bg-accent) 35%, transparent);
}

.status-pill.up {
  background: color-mix(in srgb, var(--accent-success) 18%, transparent);
  color: var(--accent-success);
}

.status-pill.down {
  background: color-mix(in srgb, var(--accent-danger) 18%, transparent);
  color: var(--accent-danger);
}

.status-pill.unknown {
  background: color-mix(in srgb, var(--text-secondary) 18%, transparent);
  color: var(--text-secondary);
}

/* Key Management Styles */
.keys-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.keys-toolbar-buttons {
  display: flex;
  gap: .5rem;
  flex-wrap: wrap;
}

.keys-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.key-card {
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 1.25rem;
  background: var(--bg-primary);
  box-shadow: var(--shadow-sm);
  transition: box-shadow .2s, border-color .2s;
}

.key-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-accent);
}

.key-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.key-card-title {
  flex: 1;
  min-width: 0;
}

.key-name {
  margin: 0 0 .25rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  word-break: break-word;
}

.key-fingerprint {
  font-family: monospace;
  font-size: .8rem;
  color: var(--text-secondary);
  word-break: break-all;
}

.key-card-actions {
  display: flex;
  gap: .5rem;
  flex-shrink: 0;
}

.key-detail-list {
  display: flex;
  flex-direction: column;
  gap: .2rem;
}

.btn-sm {
  padding: .35rem .6rem;
  font-size: .8rem;
}

.key-card-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: .75rem;
  margin-bottom: 1rem;
}

.key-meta-item {
  display: flex;
  flex-direction: column;
  gap: .2rem;
}

.key-meta-label {
  font-size: .8rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: .04em;
}

.key-meta-value {
  font-size: .9rem;
  color: var(--text-primary);
  font-weight: 500;
}

.key-used {
  color: var(--accent-success);
}

.key-unused {
  color: var(--text-secondary);
}

.key-usage-list {
  border-top: 1px solid var(--border-primary);
  padding-top: 1rem;
}

.key-usage-title {
  margin: 0 0 .75rem;
  font-size: .9rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: .04em;
}

.key-usage-device {
  display: flex;
  gap: .75rem;
  align-items: center;
  padding: .5rem;
  border-radius: 6px;
  background: var(--bg-secondary);
  margin-bottom: .5rem;
}

.key-usage-device:last-child {
  margin-bottom: 0;
}

.device-name {
  font-weight: 600;
  color: var(--text-primary);
}

.device-host {
  font-size: .85rem;
}

.device-kind {
  font-size: .85rem;
}

/* Device Filtering */
.device-filters {
  margin-bottom: 1.5rem;
}

.device-filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

/* Device Status Indicators */
.device-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: .4rem;
  font-size: .8rem;
  padding: .25rem .6rem;
  border-radius: 12px;
  font-weight: 500;
}

.device-status-online {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.device-status-offline {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.device-status-unknown {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
}

.device-status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.device-status-dot.online {
  background: #22c55e;
}

.device-status-dot.offline {
  background: #ef4444;
}

.device-status-dot.unknown {
  background: var(--text-secondary);
}

/* Dark theme status indicators */
[data-theme="dark"] .device-status-online {
  background: rgba(34, 197, 94, 0.2);
  color: #4ade80;
  border-color: rgba(34, 197, 94, 0.3);
}

[data-theme="dark"] .device-status-offline {
  background: rgba(239, 68, 68, 0.2);
  color: #f87171;
  border-color: rgba(239, 68, 68, 0.3);
}

/* Chart Container Improvements */
.metric-chart {
  position: relative;
  margin: .75rem 0;
}

.chart-container {
  position: relative;
  height: 140px;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-secondary);
  font-size: .9rem;
}


/* ==========================
   THEMED FORM CONTROLS & TEXTURES (APP-WIDE)
   ========================== */

/* Normalise form controls to theme variables */
input, select, textarea {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-secondary);
  border-radius: 6px;
}

input::placeholder, textarea::placeholder {
  color: var(--text-muted);
}

input:disabled, select:disabled, textarea:disabled {
  background: color-mix(in srgb, var(--bg-tertiary) 40%, transparent);
  color: var(--text-muted);
  cursor: not-allowed;
}

/* Consistent focus styling */
input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px color-mix(in srgb, var(--accent-primary) 20%, transparent);
}

/* Select caret */
select {
  appearance: none;
  background-image: linear-gradient(45deg, transparent 50%, var(--text-secondary) 50%),
    linear-gradient(135deg, var(--text-secondary) 50%, transparent 50%);
  background-position: calc(100% - 16px) calc(50% - 3px), calc(100% - 11px) calc(50% - 3px);
  background-size: 5px 5px, 5px 5px;
  background-repeat: no-repeat;
  padding-right: 2rem;
}

/* Dropdown options (best-effort) */
select option {
  background: var(--bg-primary);
  color: var(--text-primary);
}

/* Utility: subtle textured surface */
.texture-surface {
  background:
    radial-gradient(transparent 1px, var(--bg-primary) 1px) 0 0/4px 4px,
    linear-gradient(var(--bg-primary), var(--bg-primary));
}